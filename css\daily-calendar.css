/* Refactored based on modern CSS best practices */

/* Rule 1: Use max-width */
/* Rule 2: Use rem for scalable typography & sizing */
/* Rule 3: Use gap instead of margins in flex/grid */
/* Rule 4: Use translate instead of negative margins */
/* Rule 5: Use Logical Properties */
/* Rule 6: Use :has() for parent-based styling */
/* Rule 7: Use clamp() for responsive typography */
/* Rule 8: Use flex: 1 instead of fixed widths in Flexbox */
/* Rule 9: Use aspect-ratio */
/* Rule 10: Use color-mix() for dynamic theming */

/* Apply box-sizing globally */
*, *::before, *::after {
    box-sizing: border-box;
}

:root {
    --primary-color: #fe2c55;
    --primary-color-rgb: 254, 44, 85;
    --secondary-color: #25f4ee;
    --background-color: #121212;
    --text-color: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7); /* Added */
    --card-bg: #1e1e1e;
    --hover-bg: #2d2d2d; /* Kept for fallback or explicit use */
    --nav-bg: #1a1a1a;
    --border-color: #333;
    --grid-line-color: rgba(255, 255, 255, 0.1);
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --danger-color: #F44336;
    --border-thin: 0.0625rem; /* 1px */
    --border-medium: 0.125rem; /* 2px */
}

body.light-theme {
    --primary-color: #fe2c55;
    --primary-color-rgb: 254, 44, 85;
    --secondary-color: #17a2b8; /* Adjusted */
    --background-color: #f8f9fa;
    --text-color: #212529;
    --text-secondary: #6c757d; /* Added */
    --card-bg: #ffffff;
    --hover-bg: #e9ecef; /* Kept for fallback or explicit use */
    --nav-bg: #ffffff;
    --border-color: #dee2e6;
    --grid-line-color: rgba(0, 0, 0, 0.1);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 100vh;
    padding-block-start: 60px; /* Match extracted.html */
    margin: 0;
    display: flex;
    flex-direction: column;
}

.top-nav {
    background-color: var(--nav-bg);
    padding: 10px 30px; /* Match extracted.html */
    position: fixed;
    inset-block-start: 0; /* Rule 5 */
    inset-inline: 0; /* Rule 5 */
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); /* Match extracted.html */
    height: 60px; /* Match extracted.html */
    backdrop-filter: blur(10px); /* Match extracted.html */
}

.nav-brand {
    /* Rule 2 (rem) */
    font-size: 1.5rem; /* 24px */
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.25rem; /* 20px */
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px; /* Match extracted.html */
    border-radius: 5px; /* Match extracted.html */
    transition: background-color 0.3s;
    font-size: 16px; /* Match extracted.html */
}

.nav-links a:hover {
    background-color: var(--hover-bg);
    /* Example using Rule 10 (color-mix) - uncomment to use */
    /* background: color-mix(in srgb, var(--nav-bg) 80%, var(--text-color) 20%); */
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

.container {
    /* Rule 2 (rem) */
    padding: 1.25rem; /* 20px */
    /* Rule 1 (max-width) */
    max-width: 75rem; /* 1200px */
    width: 100%;
    margin-inline: auto; /* Rule 5 */
    /* Rule 8 (flex) */
    flex: 1;
    display: flex;
    flex-direction: column;
}

.current-task-header {
    background-color: var(--card-bg);
    /* Rule 2 (rem) */
    border-radius: 0.75rem; /* 12px */
    padding: 1.25rem; /* 20px */
    margin-block-end: 1.25rem; /* 20px - Rule 5 */
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1); /* 4px 6px */
    position: relative;
    overflow: hidden;
}

/* Task source indicators */
.current-task-header h1[data-source]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    transition: background-color 0.3s ease;
}

.current-task-header h1[data-source="calendar"]::before {
    background-color: var(--primary-color);
}

.current-task-header h1[data-source="priority"]::before {
    background-color: var(--secondary-color);
}

.calendar-container {
    /* Rule 8 (flex) */
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* Allow shrinking */
    /* Rule 2 (rem) - Height calculation for specific layout needs */
    height: calc(100vh - 13.75rem); /* 220px approx */
    overflow: hidden;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--card-bg);
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 1rem; /* 16px */
    padding-inline: 1.5rem; /* 24px */
    border-radius: 0.75rem; /* 12px */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1); /* 2px 8px */
    margin-block-end: 1.5rem; /* 24px - Rule 5 */
    flex-wrap: wrap;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1rem; /* 16px */
}

.view-controls {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.75rem; /* 12px */
    flex-wrap: wrap;
}

.view-btn {
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.625rem; /* 10px */
    padding-inline: 1.25rem; /* 20px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    background: var(--background-color);
    color: var(--text-color);
    border-radius: 0.5rem; /* 8px */
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: auto;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    position: relative; /* For pseudo-elements */
    overflow: hidden; /* For pseudo-elements */
}

.view-btn i {
    /* Rule 2 (rem) */
    font-size: 1.1em; /* Keep em for relative icon sizing */
    margin-inline-end: 0.3125rem; /* 5px - Rule 5 */
}

.view-btn:hover {
    /* Example using Rule 10 (color-mix) */
    background: color-mix(in srgb, var(--background-color) 85%, var(--text-color) 15%);
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.0625rem); /* -1px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1); /* 2px 8px */
}

/* Hover effect using pseudo-element (alternative to direct background change) */
.view-btn::after {
    content: '';
    position: absolute;
    inset: 0; /* Rule 5 */
    background: var(--primary-color);
    opacity: 0;
    transform: scale(0);
    border-radius: inherit; /* Inherit from button */
    transition: all 0.3s ease;
    z-index: -1; /* Place behind content */
}

.view-btn:hover::after {
    opacity: 0.1;
    transform: scale(1);
}


.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.75rem rgba(var(--primary-color-rgb), 0.3); /* 2px 12px */
}

.date-controls {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.25rem; /* 20px */
    /* Rule 2 (rem) */
    padding: 0.5rem; /* 8px */
    background: var(--background-color);
    border-radius: 0.75rem; /* 12px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
}

#currentDate {
    /* Rule 7 (clamp), Rule 2 (rem) */
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: 600;
    color: var(--text-color);
    min-width: auto;
    text-align: center;
    margin: 0;
}

.nav-button {
    background-color: var(--card-bg);
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    color: var(--text-color);
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.625rem; /* 10px */
    padding-inline: 1rem; /* 16px */
    border-radius: 0.5rem; /* 8px */
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    font-weight: 500;
    position: relative; /* For pseudo-elements */
    overflow: hidden; /* For pseudo-elements */
}

.nav-button:hover {
    background-color: var(--hover-bg); /* Keep explicit hover or use color-mix */
    color: var(--primary-color); /* Example hover style */
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.0625rem); /* -1px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.5rem rgba(var(--primary-color-rgb), 0.2); /* 2px 8px */
}

/* Hover effect using pseudo-element */
.nav-button::after {
    content: '';
    position: absolute;
    /* Rule 5 */
    inset-block-start: 50%;
    inset-inline-start: 50%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.nav-button:hover::after {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.5);
}

.time-settings {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
    background-color: var(--hover-bg);
    /* Rule 2 (rem) */
    padding: 0.625rem; /* 10px */
    border-radius: 0.5rem; /* 8px */
}

.time-settings input[type="time"] {
    background-color: var(--card-bg);
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    color: var(--text-color);
    /* Rule 2 (rem) */
    padding: 0.3125rem; /* 5px */
    border-radius: 0.25rem; /* 4px */
}

.settings-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.3125rem; /* 5px */
    padding-inline: 0.9375rem; /* 15px */
    border-radius: 0.25rem; /* 4px */
    cursor: pointer;
    transition: opacity 0.3s;
}

.settings-button:hover {
    opacity: 0.9;
}

.calendar-grid-container {
    display: flex;
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block: 1.25rem; /* 20px */
    background-color: var(--hover-bg);
    border-radius: 0.5rem; /* 8px */
    padding: 0.9375rem; /* 15px */
    /* Rule 8 (flex) */
    flex: 1;
    overflow: hidden;
}

main.calendar-container { /* Already defined above, ensure consistency */
    flex: 1;
    display: flex;
    flex-direction: column;
}

.time-axis {
    /* Rule 2 (rem) */
    width: 3.75rem; /* 60px - Fixed width might be needed for alignment */
    margin-inline-end: 0.625rem; /* 10px - Rule 5 */
    color: var(--text-color);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow-y: hidden;
}

.time-label {
    position: absolute;
    /* Rule 2 (rem) */
    height: 1.875rem; /* 30px */
    line-height: 1.875rem; /* 30px */
    font-size: 0.75rem; /* 12px */
    color: var(--text-color);
    opacity: 0.8;
    /* Rule 5: Position handled by JS based on time */
}

.calendar-grid {
    flex-grow: 1;
    display: grid;
    /* Rule 2 (rem) */
    grid-template-rows: repeat(48, 0.9375rem); /* 15px */
    gap: 0;
    background-color: var(--card-bg);
    border-radius: 0.375rem; /* 6px */
    padding: 0;
    position: relative;
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    min-height: 90rem; /* 1440px - Ensures 1px per minute for positioning */
    overflow-y: hidden;
}

.grid-line {
    position: absolute;
    /* Rule 5 */
    inset-inline-start: 0;
    inset-inline-end: 0;
    /* Rule 2 (rem) */
    height: var(--border-thin); /* 1px */
    background-color: var(--grid-line-color);
    /* Rule 5: Position handled by JS based on time */
}

.half-hour-line {
    /* Rule 2 (rem), Rule 5 (logical) */
    border-block-start: var(--border-thin) dashed var(--grid-line-color);
}

.hour-line {
    /* Rule 2 (rem), Rule 5 (logical) */
    border-block-start: var(--border-thin) solid var(--grid-line-color);
}

.task-block {
    position: absolute;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-inline-start: 0.3125rem; /* 5px */
    inset-inline-end: 0.3125rem; /* 5px */
    background-color: var(--primary-color);
    border-radius: 0.25rem; /* 4px */
    padding-block: 0.25rem; /* 4px */
    padding-inline: 0.5rem; /* 8px */
    color: white;
    font-size: 0.75rem; /* 12px */
    overflow: hidden;
    cursor: pointer;
    transition: opacity 0.2s;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    min-height: 1.5rem; /* 24px */
    /* Rule 5: Top/Height handled by JS */
}

.task-block .task-time {
    /* Rule 2 (rem) */
    font-size: 0.6875rem; /* 11px */
    opacity: 0.9;
    white-space: nowrap;
}

.task-block .task-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-inline-start: auto; /* Rule 5 */
    text-align: end; /* Rule 5 */
    flex-grow: 1;
}

.task-block:hover {
    opacity: 0.9;
}

.task-block.class { background-color: var(--primary-color); }
.task-block.study { background-color: var(--secondary-color); }
.task-block.break { background-color: var(--success-color); }
.task-block.free { background-color: #9E9E9E; }
.task-block.creating {
    background-color: rgba(var(--primary-color-rgb), 0.3);
    border: var(--border-medium) dashed var(--primary-color); /* Rule 2 */
    pointer-events: none;
    z-index: 100;
}


.calendar-legend {
    display: flex;
    justify-content: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.5rem; /* 24px */
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 1rem; /* 16px */
    padding-inline: 1.5rem; /* 24px */
    background: var(--card-bg);
    border-radius: 0.75rem; /* 12px */
    margin-block-start: 1.5rem; /* 24px - Rule 5 */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1); /* 2px 8px */
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    padding-inline: 1rem; /* 16px */
    background: var(--background-color);
    border-radius: 0.5rem; /* 8px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
}

.legend-color {
    /* Rule 2 (rem), Rule 9 (aspect-ratio) */
    width: 1.5rem; /* 24px */
    aspect-ratio: 1; /* 1/1 */
    border-radius: 0.375rem; /* 6px */
    border: var(--border-medium) solid var(--border-color); /* Rule 2 */
}

.class-block { background-color: var(--primary-color); }
.study-block { background-color: var(--secondary-color); }
.break-block { background-color: var(--success-color); }
.free-block { background-color: #9E9E9E; }

.event-details {
    background-color: var(--hover-bg);
    /* Rule 2 (rem) */
    border-radius: 0.5rem; /* 8px */
    padding: 0.9375rem; /* 15px */
    margin-block-start: 1.25rem; /* 20px - Rule 5 */
}

.event-block { /* Used for dragging visualization? */
    position: absolute;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-inline-start: 3.75rem; /* 60px */
    inset-inline-end: 0.625rem; /* 10px */
    padding: 0.25rem; /* 4px */
    border-radius: 0.25rem; /* 4px */
    cursor: pointer;
    color: white;
    overflow: hidden;
    transition: box-shadow 0.2s ease;
    user-select: none;
    /* Rule 5: Top/Height handled by JS */
}

.event-block.dragging {
    opacity: 0.7;
    /* Rule 2 (rem) */
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2); /* 4px 8px */
    cursor: grabbing;
}

.event-block:hover {
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1); /* 2px 4px */
}

/* Event edit/create modal styles */
.event-edit-modal,
.event-create-modal {
    display: none;
    position: fixed;
    /* Rule 5: Centering */
    inset-block-start: 50%;
    inset-inline-start: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--card-bg);
    /* Rule 2 (rem) */
    border-radius: 0.5rem; /* 8px */
    padding: 1.25rem; /* 20px */
    box-shadow: 0 0.25rem 1.25rem rgba(0, 0, 0, 0.3); /* 4px 20px */
    z-index: 1000;
    /* Rule 1 (max-width) */
    width: 90%;
    max-width: 25rem; /* 400px */
}

.event-edit-modal.show,
.event-create-modal.show {
    display: block;
}

.modal-backdrop {
    display: none;
    position: fixed;
    inset: 0; /* Rule 5 */
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.modal-backdrop.show,
.event-create-modal.show + .modal-backdrop { /* Ensure backdrop shows for create modal too */
    display: block;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 1.25rem; /* 20px */
}

.modal-title {
    /* Rule 2 (rem) */
    font-size: 1.125rem; /* 18px */
    font-weight: bold;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    /* Rule 2 (rem) */
    font-size: 1.25rem; /* 20px */
    padding: 0.3125rem; /* 5px */
}

.modal-body {
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 1.25rem; /* 20px */
    /* Specific padding for create modal if needed */
}
.event-create-modal .modal-body {
    padding: 1.25rem; /* 20px */
}


.form-group {
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.9375rem; /* 15px */
}

.form-group label {
    display: block;
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.3125rem; /* 5px */
    color: var(--text-color);
}

.form-group input,
.form-group select,
.event-create-modal .form-control { /* Combined selector */
    width: 100%;
    /* Rule 2 (rem) */
    padding: 0.5rem 0.75rem; /* 8px 12px */
    border-radius: 0.25rem; /* 4px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    background-color: var(--background-color); /* Default for edit */
    color: var(--text-color);
}
/* Specific background for create modal inputs if different */
.event-create-modal .form-control {
     background-color: var(--card-bg);
}


.modal-footer {
    display: flex;
    justify-content: flex-end;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
}

.modal-footer button,
.event-create-modal .btn { /* Combined selector */
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    padding-inline: 1rem; /* 16px */
    border-radius: 0.25rem; /* 4px */
    border: none;
    cursor: pointer;
    transition: opacity 0.2s, transform 0.2s;
}

.btn-save,
.event-create-modal .btn-primary { /* Combined */
    background-color: var(--primary-color);
    color: white;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-cancel,
.event-create-modal .btn-secondary { /* Combined */
    background-color: var(--hover-bg);
    color: var(--text-color);
    /* Add border for secondary button */
}
.event-create-modal .btn-secondary {
    background-color: transparent;
    border: var(--border-thin) solid var(--border-color);
}


.modal-footer button:hover,
.event-create-modal .btn:hover { /* Combined */
    opacity: 0.9;
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.0625rem); /* -1px */
}

/* Theme toggle button */
.theme-toggle {
    position: fixed;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-block-end: 1.25rem; /* 20px */
    inset-inline-start: 1.25rem; /* 20px */
    z-index: 1001;
    background-color: var(--card-bg);
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    color: var(--text-color);
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    padding-inline: 1rem; /* 16px */
    border-radius: 1.25rem; /* 20px */
    cursor: pointer;
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    transition: all 0.3s ease;
    /* Rule 2 (rem) */
    font-size: 0.875rem; /* 14px */
    box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1); /* 2px 10px */
}

.theme-toggle:hover {
    background-color: var(--hover-bg);
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.2); /* 4px 15px */
}

.theme-icon {
    /* Rule 2 (rem) */
    font-size: 1rem; /* 16px */
}

/* Current time indicator styles */
.current-time-indicator {
    position: absolute;
    /* Rule 5 */
    inset-inline: 0;
    /* Rule 2 (rem) */
    height: 0.1875rem; /* 3px */
    background-color: var(--primary-color);
    z-index: 200;
    pointer-events: none;
    /* Rule 2 (rem) */
    box-shadow: 0 0 0.3125rem rgba(var(--primary-color-rgb), 0.5); /* 5px */
    /* Rule 5: Top handled by JS */
}

.current-time-indicator::before {
    content: '';
    position: absolute;
    /* Rule 5 */
    inset-inline-start: 0;
    inset-block-start: -0.25rem; /* -4px */
    /* Rule 2 (rem), Rule 9 (aspect-ratio) */
    width: 0.75rem; /* 12px */
    aspect-ratio: 1;
    background-color: var(--primary-color);
    border-radius: 50%;
    /* Rule 2 (rem) */
    box-shadow: 0 0 0.3125rem rgba(var(--primary-color-rgb), 0.5); /* 5px */
}

.current-time-label {
    position: absolute;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-inline-start: 0.9375rem; /* 15px */
    inset-block-start: -0.625rem; /* -10px - Adjust based on indicator height */
    background-color: var(--primary-color);
    color: white;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.125rem; /* 2px */
    padding-inline: 0.375rem; /* 6px */
    border-radius: 0.25rem; /* 4px */
    font-size: 0.75rem; /* 12px */
    font-weight: bold;
    transform: translateY(-50%); /* Vertically center relative to indicator line */
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3); /* 2px 4px */
    white-space: nowrap;
}

.sleep-schedule {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--card-bg);
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.9375rem; /* 15px */
    padding-inline: 1.25rem; /* 20px */
    border-radius: 0.75rem; /* 12px */
    margin-block-end: 1.25rem; /* 20px - Rule 5 */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2); /* 2px 8px */
}

.schedule-item {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
}

.schedule-item i {
    /* Rule 2 (rem) */
    font-size: 1.5rem; /* 24px */
    color: var(--primary-color);
}

.time-info {
    display: flex;
    flex-direction: column;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.25rem; /* 4px */
}

.buffer-time {
    /* Rule 2 (rem) */
    font-size: 0.85em; /* Keep em */
    opacity: 0.8;
}

.total-time {
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    padding-inline: 0.9375rem; /* 15px */
    background: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 0.5rem; /* 8px */
    border: var(--border-thin) solid rgba(var(--primary-color-rgb), 0.2); /* Rule 2 */
}

.total-time i {
    color: var(--secondary-color);
}

/* Sleep Schedule Visual Indicators */
.sleep-indicator {
    position: absolute;
    width: 100%;
    background: linear-gradient(90deg,
        rgba(255, 223, 0, 0.1) 0%,
        rgba(255, 223, 0, 0.3) 50%,
        rgba(255, 223, 0, 0.1) 100%);
    pointer-events: none;
    z-index: 1;
    /* Rule 5: Top/Height handled by JS */
}

.sleep-indicator::after {
    content: '';
    position: absolute;
    /* Rule 5 */
    inset-inline-start: 0;
    width: 100%;
    /* Rule 2 (rem) */
    height: 0.125rem; /* 2px */
    background-color: rgba(255, 223, 0, 0.8);
    /* Rule 5 */
    inset-block-start: 50%;
    transform: translateY(-50%);
}

.wake-buffer-zone,
.sleep-buffer-zone {
    position: absolute; /* Added for positioning */
    width: 100%; /* Added */
    inset-inline: 0; /* Added Rule 5 */
    pointer-events: none; /* Added */
    z-index: 0; /* Added */
    /* Rule 5: Top/Height handled by JS */
}

.wake-buffer-zone {
    background: linear-gradient(180deg,
        rgba(255, 223, 0, 0.2) 0%,
        rgba(255, 223, 0, 0.05) 100%);
}

.sleep-buffer-zone {
    background: linear-gradient(180deg,
        rgba(255, 223, 0, 0.05) 0%,
        rgba(255, 223, 0, 0.2) 100%);
}

.time-summary {
    background: var(--card-bg);
    /* Rule 2 (rem) */
    padding: 0.9375rem; /* 15px */
    border-radius: 0.75rem; /* 12px */
    margin-block-end: 1.25rem; /* 20px - Rule 5 */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1); /* 2px 8px */
}

.time-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.25rem; /* 20px */
}

.stat-item {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    padding-inline: 0.9375rem; /* 15px */
    background: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 0.5rem; /* 8px */
    border: var(--border-thin) solid rgba(var(--primary-color-rgb), 0.2); /* Rule 2 */
}

.stat-item i {
    color: var(--primary-color);
    /* Rule 2 (rem) */
    font-size: 1.2em; /* Keep em */
}

/* Calendar View Styles */
.calendar-views {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 1.25rem; /* 20px */
}

/* .view-btn already styled above */

/* Month Grid Styles */
.month-grid-view { /* Renamed class to avoid conflict with .month-grid inside year view */
    background: var(--card-bg);
    /* Rule 2 (rem) */
    border-radius: 0.75rem; /* 12px */
    padding: 1.5rem; /* 24px */
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); /* 4px 12px */
    margin-inline: auto; /* Rule 5 */
    /* Rule 1 (max-width) */
    max-width: 87.5rem; /* 1400px */
    width: 100%;
    display: flex; /* Added for structure */
    flex-direction: column; /* Added */
}

.week-header { /* Shared between month/year views */
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.75rem; /* 12px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 1rem; /* 16px */
    padding-block: 0.5rem; /* 8px */
    border-block-end: var(--border-medium) solid var(--border-color); /* 2px */
}

.week-header > div {
    text-align: center;
    font-weight: 600;
    color: var(--text-color);
    font-size: 1em; /* Keep em */
    /* Rule 2 (rem) */
    padding: 0.5rem; /* 8px */
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    /* Rule 8 (flex) */
    flex: 1;
    align-content: start;
}

.day-cell {
    /* Rule 9 (aspect-ratio) */
    aspect-ratio: 1; /* Keep square */
    /* Rule 2 (rem) */
    padding: 0.5rem; /* 8px */
    border-radius: 0.5rem; /* 8px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    /* Rule 2 (rem) */
    min-height: 5rem; /* 80px - adjust as needed with aspect-ratio */
    position: relative; /* For :has background potentially */
}

.day-cell:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1); /* 4px 8px */
}

/* Rule 6 (:has) example */
.day-cell:has(.task-count) {
     /* Subtle highlight - Rule 10 (color-mix) */
    background-color: color-mix(in srgb, var(--card-bg) 95%, var(--text-color) 5%);
}
.day-cell:has(.task-count.completed):not(:has(.task-count:not(.completed))) {
     /* If ALL tasks are completed - Rule 10 (color-mix) */
    background-color: color-mix(in srgb, var(--success-color) 10%, var(--card-bg) 90%);
}


.day-cell.empty {
    background: var(--hover-bg);
    cursor: default;
    opacity: 0.6; /* Added */
}

.day-cell.success { background: rgba(76, 175, 80, 0.1); border-color: rgba(76, 175, 80, 0.3); }
.day-cell.warning { background: rgba(255, 193, 7, 0.1); border-color: rgba(255, 193, 7, 0.3); }
.day-cell.danger { background: rgba(244, 67, 54, 0.1); border-color: rgba(244, 67, 54, 0.3); }

.day-cell.today {
    border: var(--border-medium) solid var(--primary-color); /* Rule 2 */
    font-weight: bold;
    background: rgba(var(--primary-color-rgb), 0.05); /* Subtle background */
}

.day-number {
    /* Rule 2 (rem) */
    font-size: 0.875rem; /* 14px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.25rem; /* 4px */
}

.task-indicators {
    margin-block-start: auto; /* Rule 5 */
    text-align: center;
    display: flex; /* Added for potential multiple indicators */
    justify-content: center; /* Added */
    gap: 0.25rem; /* Rule 2, Rule 3 */
}

.task-count {
    background: var(--primary-color);
    color: white;
    /* Rule 2 (rem) */
    padding: 0.125rem 0.375rem; /* 2px 6px */
    border-radius: 0.625rem; /* 10px */
    font-size: 0.6875rem; /* 11px */
    transition: all 0.3s ease;
    line-height: 1; /* Ensure consistent height */
}

.task-count.completed { background: var(--success-color); }

/* Week View Styles */
.week-view {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1rem; /* 16px */
    /* Rule 2 (rem) */
    padding: 1.5rem; /* 24px */
    background: var(--card-bg);
    border-radius: 0.75rem; /* 12px */
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); /* 4px 12px */
    /* Rule 2 (rem) */
    height: calc(100% - 1.5rem); /* 24px */
    margin-inline: auto; /* Rule 5 */
    /* Rule 1 (max-width) */
    max-width: 87.5rem; /* 1400px */
    width: 100%;
}

.week-day {
    background: var(--background-color);
    /* Rule 2 (rem) */
    border-radius: 0.625rem; /* 10px */
    padding: 1rem; /* 16px */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    min-height: 15rem; /* 240px */
    display: flex;
    flex-direction: column;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.75rem; /* 12px */
    transition: all 0.2s ease;
}

.week-day:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); /* 4px 12px */
}

.day-header {
    text-align: center;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block-end: 0.625rem; /* 10px */
    border-block-end: var(--border-thin) solid var(--border-color); /* 1px */
    margin-block-end: 0.625rem; /* 10px */
}

.day-name {
    font-weight: 500;
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-inline-end: 0.3125rem; /* 5px */
}

/* Year View Styles */
.year-view {
    display: flex; /* Changed to flex for easier wrapping control if needed */
    flex-direction: column;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 2rem; /* 32px */
    /* Rule 2 (rem) */
    padding: 1.5rem; /* 24px */
    margin-inline: auto; /* Rule 5 */
    /* Rule 1 (max-width) */
    max-width: 87.5rem; /* 1400px */
    width: 100%;
    /* Rule 2 (rem) */
    height: calc(100% - 1.5rem); /* 24px */
    overflow-y: auto;
}

.year-quarter {
    display: flex; /* Using flex column */
    flex-direction: column;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.5rem; /* 24px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 2rem; /* 32px */
}

.quarter-title { /* Added for structure */
    font-size: 1.5rem; /* Rule 2 */
    font-weight: 600;
    margin-block-end: 1rem; /* Rule 2, Rule 5 */
    padding-inline-start: 1rem; /* Rule 2, Rule 5 */
    border-inline-start: 0.25rem solid var(--primary-color); /* Rule 2, Rule 5 */
}


.quarter-months {
    display: grid; /* Changed to grid for layout */
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 20rem), 1fr)); /* Responsive columns */
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.5rem; /* 24px */
}

.year-month {
    background: var(--card-bg);
    /* Rule 2 (rem) */
    border-radius: 0.75rem; /* 12px */
    padding: 1rem; /* 16px */
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); /* 4px 12px */
    display: flex;
    flex-direction: column;
    /* Rule 2 (rem) */
    min-height: 17.5rem; /* 280px */
    transition: all 0.2s ease;
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    /* Rule 1 (max-width) - Handled by grid column */
    width: 100%;
    overflow: hidden;
}

.year-month:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); /* 8px 16px */
    border-color: var(--primary-color);
}


.month-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.5rem; /* 8px */
    border-block-end: var(--border-medium) solid var(--border-color); /* 2px */
    margin-block-end: 0.5rem; /* 8px */
}

.month-name {
    /* Rule 2 (rem) */
    font-size: 1.2em; /* Keep em */
    font-weight: 600;
    color: var(--text-color);
}


/* .month-grid is used inside year-month */
.year-month .month-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.125rem; /* 2px */
    /* Rule 2 (rem) */
    font-size: 0.85em; /* Keep em */
    /* Rule 8 (flex) */
    flex: 1;
    /* Rule 2 (rem) */
    padding: 0.25rem; /* 4px */
    /* Reset styles from top-level month grid */
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    margin: 0;
    max-width: none;
    min-height: auto; /* Reset min-height */
    border: none;
}

.year-month .month-grid .week-header {
    display: grid; /* Keep display grid */
    grid-template-columns: repeat(7, 1fr);
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.125rem; /* 2px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.25rem; /* 4px */
    padding-block: 0.125rem; /* 2px */
    border-block-end: var(--border-thin) solid var(--border-color); /* 1px */
    /* Reset padding from top-level week-header */
    padding-inline: 0;
}

.year-month .month-grid .week-header > div {
    text-align: center;
    font-weight: 500;
    /* Rule 2 (rem) */
    font-size: 0.8em; /* Keep em */
    color: var(--text-secondary);
    /* Rule 2 (rem) */
    padding: 0.125rem; /* 2px */
}

.year-month .month-grid .days-grid { /* Added wrapper for days */
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.125rem; /* 2px - Rule 2, Rule 3 */
    flex: 1; /* Allow grid to take space */
}


.year-month .month-grid .day { /* Target day specifically inside year view */
    /* Rule 9 (aspect-ratio) */
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* Rule 2 (rem) */
    padding: 0.125rem; /* 2px */
    border-radius: 0.25rem; /* 4px */
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    /* Rule 2 (rem) */
    font-size: 0.9em; /* Keep em */
    border: var(--border-thin) solid transparent; /* Add border for spacing consistency */
    min-height: auto; /* Override from .day-cell */
}

.year-month .month-grid .day:not(.empty):hover {
    background: var(--hover-bg);
    transform: scale(1.1);
    z-index: 2;
}

.year-month .month-grid .day.empty {
    color: var(--text-secondary);
    opacity: 0.5;
    cursor: default;
    background: transparent; /* Override hover */
}

.year-month .month-grid .day.today {
    background: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color); /* Rule 2 */
    font-weight: bold;
}


.year-month .month-grid .day.success { background: rgba(76, 175, 80, 0.1); border-color: rgba(76, 175, 80, 0.3); }
.year-month .month-grid .day.warning { background: rgba(255, 193, 7, 0.1); border-color: rgba(255, 193, 7, 0.3); }
.year-month .month-grid .day.danger { background: rgba(244, 67, 54, 0.1); border-color: rgba(244, 67, 54, 0.3); }

.year-month .month-grid .day .task-count {
    position: absolute;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-block-end: 0.0625rem; /* 1px */
    inset-inline-end: 0.0625rem; /* 1px */
    /* Rule 2 (rem) */
    min-width: 0.75rem; /* 12px */
    height: 0.75rem; /* 12px */
    background: var(--primary-color);
    color: white;
    border-radius: 0.375rem; /* 6px */
    font-size: 0.7em; /* Keep em */
    display: flex;
    align-items: center;
    justify-content: center;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding: 0 0.125rem; /* 2px */
    line-height: 1; /* Added */
}

.year-month .month-grid .day.success .task-count { background: var(--success-color); }
.year-month .month-grid .day.warning .task-count { background: var(--warning-color); }
.year-month .month-grid .day.danger .task-count { background: var(--danger-color); }

.year-month .month-grid .day.has-events::after { /* Small dot indicator */
    content: '';
    position: absolute;
    /* Rule 2 (rem), Rule 5 (logical) */
    inset-block-end: 0.125rem; /* 2px */
    /* Rule 2 (rem), Rule 9 (aspect-ratio) */
    width: 0.1875rem; /* 3px */
    aspect-ratio: 1;
    border-radius: 50%;
    background: var(--primary-color);
    /* Hide if task count is shown */
}
.year-month .month-grid .day:has(.task-count).has-events::after {
    display: none;
}


.month-stats {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    align-items: center;
}

.month-stat-indicator {
    /* Rule 2 (rem), Rule 9 (aspect-ratio) */
    width: 0.5rem; /* 8px */
    aspect-ratio: 1;
    border-radius: 50%;
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-inline-end: 0.125rem; /* 2px */
}

.month-stat-indicator.success { background: var(--success-color); }
.month-stat-indicator.warning { background: var(--warning-color); }
.month-stat-indicator.danger { background: var(--danger-color); }

.month-summary {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.75rem; /* 12px */
    margin-block-start: 0.5rem; /* Added spacing - Rule 2, Rule 5 */
    flex-wrap: wrap; /* Allow wrapping */
}

.month-stat {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.25rem; /* 4px */
    /* Rule 2 (rem) */
    font-size: 0.8em; /* Keep em */
    /* Rule 2 (rem), Rule 5 (logical) */
    padding-block: 0.125rem; /* 2px */
    padding-inline: 0.375rem; /* 6px */
    border-radius: 0.25rem; /* 4px */
    background: var(--background-color);
    border: var(--border-thin) solid transparent; /* Default border */
}

.month-stat.success { background: rgba(76, 175, 80, 0.1); border-color: rgba(76, 175, 80, 0.3); }
.month-stat.warning { background: rgba(255, 193, 7, 0.1); border-color: rgba(255, 193, 7, 0.3); }
.month-stat.danger { background: rgba(244, 67, 54, 0.1); border-color: rgba(244, 67, 54, 0.3); }

/* Task Details Panel (If used) */
.task-details {
    background: var(--card-bg);
    /* Rule 2 (rem) */
    border-radius: 0.75rem; /* 12px */
    padding: 1.25rem; /* 20px */
    margin-block-start: 1.25rem; /* 20px - Rule 5 */
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1); /* 4px 15px */
}

.task-details h3 {
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.9375rem; /* 15px */
    color: var(--text-color);
}

.project-info {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.9375rem; /* 15px */
}

.description {
    color: var(--text-secondary);
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.9375rem; /* 15px */
    line-height: 1.5;
}

.meta-info {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 1.25rem; /* 20px */
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 1.25rem; /* 20px */
    flex-wrap: wrap; /* Added */
}

.due-date, .priority {
    display: flex;
    align-items: center;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.5rem; /* 8px */
    color: var(--text-secondary);
}

.priority.high { color: var(--primary-color); }
.priority.medium { color: var(--warning-color); } /* Use variable */
.priority.low { color: var(--success-color); } /* Use variable */

.action-buttons {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.625rem; /* 10px */
}

/* Task Item Enhancements (e.g., in Week View or a list) */
.task-item {
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex; /* Added for layout */
    align-items: center; /* Added */
    gap: 0.5rem; /* Rule 2, Rule 3 */
    padding: 0.5rem; /* Rule 2 */
    border-radius: 0.25rem; /* Rule 2 */
}

.task-item:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateX(0.3125rem); /* 5px */
    background: var(--hover-bg);
}

.task-item.completed {
    opacity: 0.7;
    background: rgba(var(--success-color), 0.1); /* Use variable */
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: var(--text-secondary);
}

.task-item.completed .task-project,
.task-item.completed .task-section {
    opacity: 0.7;
}

.task-content {
    flex-grow: 1;
    overflow: hidden; /* Prevent text overflow issues */
}

.task-project {
    /* Rule 2 (rem) */
    font-size: 0.6875rem; /* 11px */
    color: var(--primary-color);
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-end: 0.125rem; /* 2px */
    white-space: nowrap; /* Added */
    overflow: hidden; /* Added */
    text-overflow: ellipsis; /* Added */
}

.task-title { /* Assuming this is the main title within task-content */
     font-weight: 500; /* Added */
     white-space: nowrap; /* Added */
     overflow: hidden; /* Added */
     text-overflow: ellipsis; /* Added */
     margin-block-end: 0.125rem; /* Rule 2, Rule 5 */
}


.task-section {
    /* Rule 2 (rem) */
    font-size: 0.6875rem; /* 11px */
    color: var(--text-secondary);
    /* Rule 2 (rem), Rule 5 (logical) */
    margin-block-start: 0.125rem; /* 2px */
    white-space: nowrap; /* Added */
    overflow: hidden; /* Added */
    text-overflow: ellipsis; /* Added */
}

.task-actions {
    display: flex;
    /* Rule 2 (rem), Rule 3 (gap) */
    gap: 0.3125rem; /* 5px */
    margin-inline-start: auto; /* Rule 5 */
}

.complete-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    /* Rule 2 (rem) */
    padding: 0.125rem; /* 2px */
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex; /* Center icon */
    align-items: center; /* Center icon */
    justify-content: center; /* Center icon */
}

.complete-btn:hover {
    color: var(--primary-color);
    transform: scale(1.2);
}

/* Animation for view transitions */
.calendar-content {
    /* Rule 8 (flex) */
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease; /* Adjusted */
}

.calendar-content.transitioning {
    opacity: 0;
    transform: scale(0.98);
}

/* Dark Theme Enhancements - Using attribute selector */
:root[data-theme="dark"] {
    --text-color: rgba(255, 255, 255, 0.95);
    --text-secondary: rgba(255, 255, 255, 0.7);
    --border-color: rgba(255, 255, 255, 0.15); /* Slightly increased visibility */
    --card-bg: #1e1e1e;
    --background-color: #121212;
    --hover-bg: #2d2d2d;
    --nav-bg: #1a1a1a;
}

/* Interaction Improvements */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.0625rem); /* -1px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15); /* 2px 8px */
}

/* Accessibility Improvements */
/* Using :focus-visible for keyboard navigation focus */
:is(.nav-links a, .view-btn, .nav-button, .settings-button, .task-block,
   .modal-close, .modal-footer button, .theme-toggle, .day-cell,
   .year-month .month-grid .day, .complete-btn, .task-item):focus-visible {
    outline: var(--border-medium) solid var(--primary-color); /* Rule 2 */
    outline-offset: 0.125rem; /* Rule 2 */
    box-shadow: 0 0 0 0.1875rem rgba(var(--primary-color-rgb), 0.3); /* Rule 2 */
}

/* Remove default outline when :focus-visible is supported */
:is(.nav-links a, .view-btn, .nav-button, .settings-button, .task-block,
   .modal-close, .modal-footer button, .theme-toggle, .day-cell,
   .year-month .month-grid .day, .complete-btn, .task-item):focus {
   outline: none;
}


/* Remove outline from containers when children are focused */
:is(.event-edit-modal, .event-create-modal, .calendar-container):focus-within {
    outline: none;
}

/* Loading States */
.calendar-loading { /* Apply to the container being loaded */
    opacity: 0.7;
    pointer-events: none;
    position: relative; /* Needed for pseudo-element */
}

.calendar-loading::after {
    content: '';
    position: absolute;
    /* Rule 5 */
    inset-block-start: 50%;
    inset-inline-start: 50%;
    /* Rule 4 (translate) */
    transform: translate(-50%, -50%);
    /* Rule 2 (rem) */
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
    border: var(--border-medium) solid var(--primary-color); /* Rule 2 */
    border-radius: 50%;
    border-block-start-color: transparent; /* Rule 5 */
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}


/* Responsive Layout Improvements */
/* Rule 2: Use rem for media query breakpoints if desired, or keep px */
@media (max-width: 87.5rem) { /* 1400px */
   /* Adjustments for year view columns if needed, handled by auto-fit now */
}

@media (max-width: 75rem) { /* 1200px */
    .quarter-months {
        /* Example: force 2 columns */
        /* grid-template-columns: repeat(2, 1fr); */
    }
    .week-view, .month-grid-view {
        max-width: 62.5rem; /* 1000px */
    }
}

@media (max-width: 62rem) { /* 992px */
    .calendar-header {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem; /* Rule 2 */
    }
    .view-controls { justify-content: center; }
    .date-controls { justify-content: center; }

    .week-view {
        grid-template-columns: repeat(auto-fit, minmax(min(100%, 10rem), 1fr)); /* More responsive */
        padding: 1rem; /* Rule 2 */
        gap: 0.75rem; /* Rule 2 */
    }
    .quarter-months {
        /* Example: force 1 column */
        grid-template-columns: 1fr;
    }
}

@media (max-width: 48rem) { /* 768px */
    .year-view {
       /* Handled by flex/grid */
        gap: 1rem; /* Rule 2 */
        padding: 1rem; /* Rule 2 */
    }
    .week-view {
        /* Adjust minmax if needed */
    }
    .view-btn {
        min-width: auto;
        padding-block: 0.5rem; /* 8px */
        padding-inline: 1rem; /* 16px */
    }
    .calendar-legend {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem; /* Rule 2 */
    }
    .day-cell {
        min-height: 4rem; /* Adjust if needed - Rule 2 */
    }
}

@media (max-width: 36rem) { /* 576px */
    .week-view {
       /* Adjust minmax if needed */
       grid-template-columns: repeat(auto-fit, minmax(min(100%, 8rem), 1fr));
    }
    .calendar-header {
        padding: 0.75rem; /* Rule 2 */
    }
    .date-controls {
        flex-direction: column;
        gap: 0.75rem; /* Rule 2 */
    }
    #currentDate {
        font-size: 1.1em; /* Keep em */
        min-width: 0;
    }
    .day-cell {
        min-height: 3.75rem; /* 60px - Rule 2 */
        padding: 0.25rem; /* Rule 2 */
    }
     .day-number {
        font-size: 0.75rem; /* Rule 2 */
    }
    .year-month {
        min-height: auto; /* Let content define height */
        padding: 0.75rem; /* Rule 2 */
    }
    .container { /* Reduce padding on smallest screens */
        padding: 0.5rem; /* Rule 2 */
    }
    .year-month .month-grid {
        font-size: 0.8em; /* Keep em */
        gap: 0.0625rem; /* 1px */
    }
    .month-header {
        padding-block: 0.375rem; /* 6px */
    }
    .task-block {
        font-size: 0.6875rem; /* 11px */
        padding-block: 0.125rem; /* 2px */
        padding-inline: 0.25rem; /* 4px */
        gap: 0.25rem; /* Rule 2 */
    }
}