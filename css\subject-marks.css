/* subject-marks.css - Styles for the subject marks page */

:root {
    --primary-color: #fe2c55;
    --secondary-color: #25f4ee;
    --background-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --hover-bg: #2d2d2d;
    --nav-bg: #1a1a1a;
    --border-color: #333;
    --form-control-color: #e0e0e0;
    --form-select-color: #e0e0e0;
    --form-label-color: #e0e0e0;
    --category-text-color: #e0e0e0;
    --progress-bg: #2d2d2d;
}

body.light-theme {
    --background-color: #f8f9fa;
    --text-color: #212529;
    --card-bg: #ffffff;
    --hover-bg: #e9ecef;
    --nav-bg: #ffffff;
    --border-color: #dee2e6;
    --form-control-color: #212529;
    --form-select-color: #212529;
    --form-label-color: #212529;
    --category-text-color: #212529;
    --progress-bg: #e9ecef;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 100vh;
    padding-top: 60px; /* Match extracted.html */
    margin: 0;
}

.top-nav {
    background-color: var(--nav-bg);
    padding: 10px 30px; /* Match extracted.html */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    height: 60px; /* Match extracted.html */
    backdrop-filter: blur(10px); /* Match extracted.html */
}

.nav-brand {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-links a:hover {
    background-color: var(--hover-bg);
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

.container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;
}

.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #e62548;
    transform: translateY(-1px);
}

.progress {
    height: 20px;
    background-color: var(--progress-bg);
}

.progress-bar {
    background-color: var(--primary-color);
}

.mark-entry {
    border-bottom: 1px solid var(--border-color);
    padding: 15px 0;
}

.mark-entry:last-child {
    border-bottom: none;
}

.weightage-slider {
    margin-top: 10px;
}

.theme-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1001;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
    background-color: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-icon {
    font-size: 16px;
}

.theme-text {
    font-size: 14px;
}

/* Form control styles */
.form-control, .form-select {
    color: var(--form-control-color) !important;
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
}

.form-control:focus, .form-select:focus {
    color: var(--form-control-color);
    background-color: var(--card-bg);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(254, 44, 85, 0.25);
}

/* Form labels */
.form-group label {
    color: var(--form-label-color);
}

/* Category text colors */
#categoryContributions .d-flex span {
    color: var(--category-text-color);
}

/* Progress bar background */
.progress {
    background-color: var(--progress-bg);
}

/* List group items in dark mode */
.list-group-item {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

/* Alert colors */
.alert-info {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

/* Text colors for specific elements */
.text-muted {
    color: #a0a0a0 !important;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
}

/* Option text color in select */
option {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Subject Performance text colors */
#subjectPerformance p {
    color: var(--text-color);
}

#subjectPerformance p span {
    color: var(--text-color);
    font-weight: 500;
}
