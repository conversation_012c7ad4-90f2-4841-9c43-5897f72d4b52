/* styles.css */
body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background: #f5f5f5;
}
.container {
  max-width: 900px;
  margin: 30px auto;
  padding: 0 20px;
}
h1 {
  text-align: center;
  margin-bottom: 20px;
}
.api-keys,
.label-search,
.image-search {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
}
.api-keys input,
.label-search input,
.image-search input {
  padding: 8px;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.label-search button {
  padding: 8px 12px;
  font-size: 1rem;
  border: none;
  background: #0073e6;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}
.label-search button:hover {
  background: #005bb5;
}
#imgPreview {
  display: block;
  max-width: 200px;
  margin: 0 auto;
  border-radius: 8px;
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}
.card {
  display: block;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  overflow: hidden;
  text-decoration: none;
  color: inherit;
}
.card img {
  width: 100%;
  display: block;
}
.card h4 {
  margin: 10px;
  font-size: 0.9rem;
  line-height: 1.2;
}
.badge {
  display: inline-block;
  margin: 0 10px 10px;
  padding: 2px 6px;
  font-size: 0.75rem;
  background: #eef;
  border-radius: 4px;
}
.history {
  margin-top: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
  padding: 16px;
}
.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.95rem;
}
.history-item:last-child {
  border-bottom: none;
}
.history-item button {
  background: #e6e6e6;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}
.history-item button:hover {
  background: #0073e6;
  color: #fff;
}

.error {
  color: #d32f2f;
  font-weight: bold;
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;
  background-color: #ffebee;
  border-radius: 4px;
  border: 1px solid #ffcdd2;
}

.loading {
  color: #1976d2;
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;
  background-color: #e3f2fd;
  border-radius: 4px;
  border: 1px solid #bbdefb;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}