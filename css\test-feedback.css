/* Test Feedback Styles */

:root {
    /* Base colors */
    --correct-color: #28a745;
    --incorrect-color: #dc3545;
    --partial-color: #ffc107;
    --neutral-color: #6c757d;

    /* Dark theme text colors with better contrast */
    --correct-text: #4eff7e;
    --incorrect-text: #ff6b7d;
    --partial-text: #ffdb4d;
    --neutral-text: #adb5bd;
    --feedback-text: #ffffff;
}

/* Header Styles */
.test-feedback-header {
    text-align: center;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.test-feedback-header h1 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.test-feedback-header .lead {
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
}

/* Card Styles */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Input Method Cards */
.input-method-card {
    height: 100%;
    background-color: var(--surface-color);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.input-method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.input-method-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.input-method-header i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.input-method-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.input-method-body {
    padding: 1.5rem;
}

/* Form Controls */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    outline: none;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* Drop Zone */
.drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--card-bg);
    margin-bottom: 1rem;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.drop-zone:hover, .drop-zone.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.drop-zone-prompt {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.drop-zone-prompt i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.drop-zone-prompt p {
    margin: 0;
    color: var(--text-color);
}

.drop-zone-prompt p.small {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Uploaded Images */
.uploaded-images {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.uploaded-image-item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.uploaded-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.uploaded-image-item .remove-image {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: rgba(var(--primary-color-rgb), 0.9);
    transform: translateY(-2px);
}

.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Results Section */
.results-section {
    margin-top: 2rem;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

/* Score Display */
.score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: var(--card-bg);
    border: 8px solid var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.score-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.score-label {
    font-size: 1.25rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    height: 100%;
}

.stat-item {
    background-color: var(--surface-color);
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-color);
}

#correctCount {
    color: var(--correct-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

#incorrectCount {
    color: var(--incorrect-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

#partialCount {
    color: var(--partial-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

/* Detailed Feedback */
.detailed-feedback, .improvement-suggestions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.detailed-feedback h3, .improvement-suggestions h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-weight: 600;
}

.feedback-content, .improvement-content {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    color: var(--feedback-text);
}

/* Question Items */
.question-item {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.question-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.question-number {
    background-color: var(--primary-color);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.question-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-correct {
    background-color: rgba(40, 167, 69, 0.2);
    color: var(--correct-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.status-incorrect {
    background-color: rgba(220, 53, 69, 0.2);
    color: var(--incorrect-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.status-partial {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--partial-text);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.question-text {
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--feedback-text);
    font-size: 1.1rem;
    line-height: 1.5;
}

.answer-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.answer-label {
    font-weight: 600;
    color: var(--neutral-text);
    font-size: 1.05rem;
}

.user-answer, .correct-answer {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    color: var(--feedback-text);
    font-weight: 500;
}

.user-answer.incorrect {
    border-color: var(--incorrect-color);
    background-color: rgba(220, 53, 69, 0.15);
    color: var(--incorrect-text);
}

.user-answer.correct {
    border-color: var(--correct-color);
    background-color: rgba(40, 167, 69, 0.15);
    color: var(--correct-text);
}

.user-answer.partial {
    border-color: var(--partial-color);
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--partial-text);
}

.feedback-text {
    background-color: rgba(var(--primary-color-rgb), 0.15);
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
    color: var(--feedback-text);
    font-weight: 500;
    line-height: 1.6;
}

/* Improvement Items */
.improvement-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.improvement-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.improvement-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.improvement-content {
    flex: 1;
}

.improvement-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.improvement-description {
    color: var(--neutral-text);
    font-weight: 500;
    line-height: 1.6;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-text {
    color: white;
    font-size: 1.5rem;
    margin-top: 1rem;
    font-weight: 600;
}

.loading-subtext {
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
}

/* Error Message */
.error-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.error-message i {
    font-size: 1.5rem;
}

/* GPAce Branding */
.gpace-branding {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .results-actions {
        margin-top: 1rem;
        width: 100%;
        justify-content: space-between;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .score-display {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .test-feedback-header h1 {
        font-size: 1.75rem;
    }

    .card-header h2 {
        font-size: 1.25rem;
    }

    .input-method-header h3 {
        font-size: 1.1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .score-circle {
        width: 120px;
        height: 120px;
    }

    .score-value {
        font-size: 2rem;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    left: auto;
    transform: none;
    background-color: var(--card-bg);
    color: var(--text-color);
    padding: 14px 20px;
    border-radius: 10px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: none;
    font-size: 0.95rem;
    max-width: 350px;
    min-width: 280px;
    text-align: left;
    border-left: none;
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
}

@keyframes slideIn {
    from {
        transform: translateX(100px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification::before {
    content: '';
    display: block;
    width: 20px;
    height: 20px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    flex-shrink: 0;
}

.notification.success {
    background-color: rgba(40, 167, 69, 0.95);
    color: white;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.notification.success::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
}

.notification.error {
    background-color: rgba(220, 53, 69, 0.95);
    color: white;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.notification.error::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 11c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1 4h-2v-2h2v2z'/%3E%3C/svg%3E");
}

.notification.info {
    background-color: rgba(13, 110, 253, 0.95);
    color: white;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.notification.info::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3C/svg%3E");
}

.notification.warning {
    background-color: rgba(255, 193, 7, 0.95);
    color: #212529;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.notification.warning::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23212529'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z'/%3E%3C/svg%3E");
}

/* Light Mode Adjustments */
@media (prefers-color-scheme: light) {
    :root {
        /* Light theme text colors with better contrast */
        --correct-text: #0e6e29;
        --incorrect-text: #a71d2a;
        --partial-text: #9c6500;
        --neutral-text: #495057;
        --feedback-text: #212529;
    }

    .user-answer, .correct-answer {
        background-color: rgba(255, 255, 255, 0.7);
        color: var(--feedback-text);
    }

    .feedback-content, .improvement-content {
        background-color: rgba(255, 255, 255, 0.7);
    }

    .feedback-text {
        background-color: rgba(var(--primary-color-rgb), 0.05);
        color: var(--feedback-text);
    }

    .status-correct,
    .status-incorrect,
    .status-partial {
        text-shadow: none;
    }

    #correctCount,
    #incorrectCount,
    #partialCount {
        text-shadow: none;
    }
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .notification {
        bottom: 20px;
        right: 20px;
        left: 20px;
        max-width: none;
    }
}
